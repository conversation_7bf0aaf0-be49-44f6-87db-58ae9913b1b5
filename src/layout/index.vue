<template>
  <el-container class="ps-container">
    <!-- 菜单栏 -->
    <el-aside :width="collapse?'64px':'230px'" class="ps-aside sidebar-container">
      <sidebar :is-collapse="collapse" />
    </el-aside>
    <!-- 主体 -->
    <el-container class="main-container">
      <!-- 导航栏 -->
      <el-header class="nav-container" height="110px">
        <nav-tabs :getPopMsg="getPopMsg" :closeAllNotifications="() => showNotifications(true)"/>
      </el-header>
      <!-- 正文 -->
      <el-main ref="mainRef">
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="cachedViews" :max="maxCachedViews">
            <!-- 添加key会导致热更新失效 -->
            <router-view v-if="env && isRouterAlive"/>
            <router-view v-else />
          </keep-alive>
        </transition>
        <el-backtop target=".el-main" :bottom="60"></el-backtop>
        <!-- 备案号 -->
        <!-- <filings :class="{'fixed-record': fixedRecord}" /> -->
      </el-main>
    </el-container>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogSync"
      width="350px"
      top="25vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="dialogType === 'identityChange' ? false : true"
      class="tips"
    >
      <div class="dialog-content" v-if="dialogType === 'wechatBind'">
        <div style="text-align: center;margin-bottom:20px;">
          <img src="@/assets/img/binding-wechat.png" alt="" />
        </div>
        <div>
          绑定微信后，可进行微信扫码登录，后期可在微信开放更多功能，请前往设置进行微信绑定。
        </div>
      </div>
      <div class="dialog-content" v-if="dialogType === 'identityChange'" v-loading="dialogLoading">
        <el-select
          v-model="org"
          placeholder="请下拉选择"
          class="ps-select"
          popper-class="ps-popper-select-theme"
          style="width:100%;"
        >
          <el-option
            v-for="item in orgsList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="dialogType === 'wechatBind'" class="goto-btn" @click="gotoSetting()">
          立即前往
        </el-button>
        <el-button v-if="dialogType === 'identityChange'" :disabled="dialogLoading" class="goto-btn" @click="confirmOrg()">
          确定
        </el-button>
      </span>
    </el-dialog>
    <double-factor v-if="userInfo.is_double_factor" />
    <jump-change-pwd v-if="showJumpDialog" />
    <!--阅读协议-->
    <read-agreement refs="readAgreemeet" @readAgreement="clickReadAgreement"></read-agreement>
    <!-- 服务到期弹窗 -->
    <dialog-message
      width="470px"
      title="提示"
      :show.sync="serviceDialogShow"
      customClass="expire-dialog"
      :showFooter="false"
      @close="handleClose('surve')"
    >
      <template class="expire-dialog-content">
        <span>{{ msg(0) }}：<span style="text-decoration: underline; color: blue;">{{ msg(1) }}</span>）</span>
      </template>
      <template #tool>
        <div class="dialog-footer" style="margin-top: 20px; text-align: right;" >
          <el-button class="ps-cancel-btn renew-btn" @click="goUpgrade">续费</el-button>
          <el-button class="ps-btn" @click="handleClose('surve')">暂不处理</el-button>
        </div>
      </template>
    </dialog-message>

    <!-- 问卷弹窗 - 已暂时弃用在侧边公告显示 -->
    <!-- <dialog-message
      width="470px"
      title="问卷调查"
      :show.sync="surveyDialogVisible"
      :showFooter="false"
      @close="handleClose('questionnaire')"
    >
      <template>
        <div>
          <div>
            <span>你有一份</span>
            <span class="f-w-700">{{ surveyName }}</span>
          </div>
          <div>如无法及时填写，可通过右上角通知（小喇叭）找到我~</div>
        </div>
      </template>
      <template #tool>
        <div class="dialog-footer" style="margin-top: 20px; text-align: right;" >
          <el-button @click="handleClose('questionnaire')">取 消</el-button>
          <el-button type="primary" @click="gotoPath">去填写</el-button>
        </div>
      </template>
    </dialog-message> -->
    <!-- 重要公告弹窗 -->
     <AnnouncementDialog :show.sync="announcementVisible" :dataList="allNotificaData.important_popup_messages" :showNotifications="showNotifications" @handleClose="handleCloseAnnouncementDialog"/>
  </el-container>
</template>

<script>
import sidebar from '@/layout/components/Sidebar/index.vue'
import NavTabs from '@/layout/components/NavTabs/index.vue'
import Filings from "@/components/Filings"
import DoubleFactor from './components/DoubleFactor/index.vue'
import JumpChangePwd from './components/JumpChangePwd'
import { setSessionStorage, getSessionStorage, to, deepClone } from '@/utils'
import { mapGetters, mapMutations } from "vuex"
import ReadAgreement from './components/ReadAgreement/index.vue'
import AnnouncementDialog from './components/AnnouncementDialog/index.vue'

export default {
  components: {
    sidebar,
    NavTabs,
    // eslint-disable-next-line vue/no-unused-components
    Filings,
    DoubleFactor,
    JumpChangePwd,
    ReadAgreement,
    AnnouncementDialog
  },
  provide () {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      collapse: false,
      dialogSync: false,
      organizationChange: false,
      dialogTitle: '',
      dialogType: '',
      orgsList: '',
      org: '',
      userInfo: '',
      fixedRecord: false,
      env: process.env.NODE_ENV === 'development',
      showJumpDialog: false,
      isRouterAlive: false, // 刷新当前页面
      serviceDialogShow: false,
      dialogLoading: false, // 弹窗loading
      surveyDialogVisible: false, // 调查问卷弹窗
      announcementVisible: false, // 重要公告弹窗
      surveyName: '',
      surveyId: '',
      allNotificaData: [] // 所有公告数据
    }
  },
  computed: {
    ...mapGetters([
      'stopServiceMsg'
    ]),
    cachedViews() {
      return this.$store.state.navTabs.cachedViews
    },
    key() {
      return this.$route.path
    },
    maxCachedViews() {
      return this.$route.maxCachedViews
    },
    msg() {
      return d => {
        let text = this.stopServiceMsg.msg ? this.stopServiceMsg.msg.split('：') : ''
        if (d === 0) {
          return text[0]
        } else {
          return text ? text[1].slice(0, -1) : ''
        }
      }
    }
  },
  watch: {},
  async created() {
    this.showJumpDialog = getSessionStorage('ISEXPIRECHANGEPWD') === '1'
    await this.$sleep(30) // 为了弹窗有先后，给个睡眠时间
    this.userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO')))
    // this.userInfo.is_double_factor = true
    this.isOpenDialog()
  },
  mounted() {
    // if (this.stopServiceMsg.code && this.stopServiceMsg.code === 5 && !this.dialogSync) {
    //   this.serviceDialogShow = true
    // }
  },
  methods: {
    ...mapMutations('user', ['SET_ISFIRSTGETPOPMSG']),
    // 侧边公告通知
    showNotifications(forceClose = false) {
      // 1. 先关闭所有现有通知
      const existingNotifications = document.querySelectorAll('.el-notification');
      existingNotifications.forEach(notification => {
        notification.style.display = 'none'; // 立即隐藏
        notification.remove() // 从DOM移除
      });
      // 如果强制关闭（如目前右上角退出登录），直接返回不创建新弹窗（但下次登录还会弹出）
      if (forceClose) return
      const _this = this; // 保存 Vue 实例引用
      // 创建通知管理器
      const notificationManager = {
        instances: [], // 存储所有通知实例
        isBulkClosing: false, // 新增批量关闭标记
        remainingCount: this.allNotificaData.popup_messages.length, // 新增剩余计数器
        // 添加通知到管理器
        add(instance) {
          this.instances.push(instance)
        },
        // 关闭所有通知
        closeAll(allMsgnNo, dataList) {
          this.isBulkClosing = true // 标记为批量关闭
          this.instances.forEach(instance => instance.close())
          this.instances = []
          this.remainingCount = 0 // 重置计数器
          dataList.forEach(item => {
            if (item.type && item.type === 'warning') {
              console.log('看看item', item, allMsgnNo)
              _this.readWarning(item)
            }
          })
          let arr = allMsgnNo.filter(itemIn => typeof itemIn !== 'string')
          if (arr.length) {
            _this.markAsRead(arr)
          }
          this.isBulkClosing = false
        },
        // 关闭单个通知
        closeSingle(instance, msgNo, data) {
          // 检查实例是否还在管理器中，避免重复处理
          if (!this.instances.includes(instance)) {
            return
          }

          // 先处理API调用，避免在关闭过程中出错
          if (data.type && data.type === 'warning') {
            _this.readWarning(data)
          } else {
            _this.markAsRead(msgNo)
          }

          // 从管理器中移除实例
          this.instances = this.instances.filter(item => item !== instance)
          this.remainingCount-- // 计数器减1

          // 更新所有弹窗的"关闭所有"按钮文本
          this.instances.forEach(inst => {
            const closeAllBtn = inst.$el.querySelector('#close-all-btn')
            if (closeAllBtn) {
              closeAllBtn.textContent = `关闭所有(${this.remainingCount})`
            }
          })

          // 最后关闭实例
          instance.close()
        }
      }
      // 最先的公告在前 倒序一下
      let copyNotificaData = deepClone(this.allNotificaData.popup_messages);
      let data = [...copyNotificaData].reverse();

      // 创建侧边弹窗
      for (let i = 1; i <= data.length; i++) {
        // 创建关闭所有按钮的VNode
        const closeAllBtn = this.$createElement('el-button', {
          attrs: {
            size: 'mini',
            type: 'plain',
            id: 'close-all-btn' // 添加唯一类名
          },
          on: {
            click: () => {
              const allMsgnNo = data.map(item => item.id)
              console.log('看看allmsgno', allMsgnNo, data)
              notificationManager.closeAll(allMsgnNo, data)
            }
          }
        }, `关闭所有(${data.length})`)

        // 创建关闭当前按钮的VNode
        const closeCurrentBtn = this.$createElement('el-button', {
          attrs: {
            size: 'mini',
            type: 'plain',
            style: 'margin-left: 10px;'
          },
          on: {
            click: () => {
              if (!notificationManager.isBulkClosing) {
                notificationManager.closeSingle(instance, [data[i - 1].id], data[i - 1]) // 统一通过管理器关闭
              }
            }
          }
        }, '关闭')

        // 创建查看按钮的VNode
        const detailBtn = this.$createElement('el-button', {
          attrs: {
            size: 'mini',
            type: 'primary'
          },
          on: {
            click: () => {
              let item = data[i - 1]
              console.log(item)
              let path = '/notice-and-announcement/notice_list'
              let query = {
                searchTitle: item.type === 'warning' ? undefined : item.title,
                type: item?.type || '',
                date_type: item.type === 'warning' ? item?.dateType : undefined
              }
              if (item.type === 'warning') {
                path = '/AI_forewarning/operation_warning'
              }
              if (item.notice_type === 2 || item.notice_type === 3 || item.notice_type === 4) {
                // item.notice_type 2就是跳去记账订单 3就是跳去离线订单
                let tabType = {
                  2: 'accounting',
                  3: 'deduction',
                  4: 'verification'
                }
                path = '/order_management/consumption_failure'
                query = {
                  tabType: tabType[item.notice_type]
                }
              }
              // 公告列表
              this.$router.push({
                path: path,
                query: query
              })
              // // instance.close()
              notificationManager.closeSingle(instance, [item.id], item)
            }
          }
        }, '查看');

        // 创建按钮容器
        const buttons = this.$createElement('div', {
          style: 'margin-top: 10px; position: relative; left: 65px;'
        }, [closeAllBtn, closeCurrentBtn, detailBtn]);

        // 创建通知内容
        const message = this.$createElement('div', [
          this.$createElement('p', {
            style: {
              'display': '-webkit-box',
              '-webkit-box-orient': 'vertical',
              '-webkit-line-clamp': '2', // 限制两行
              'overflow': 'hidden',
              'text-overflow': 'ellipsis', // 超出显示省略号
              'word-break': 'break-all' // 防止长单词/数字溢出
            }
          }, data[i - 1].title),
          buttons
        ]);

        // 创建通知实例
        const instance = this.$notify({
          title: '提示',
          duration: 0, // 永不自动关闭
          message: message,
          showClose: true, // 禁用默认关闭按钮
          customClass: 'custom-notify',
          position: 'top-right',
          // offset: 100 + (i - 1) * 80, // 垂直偏移避免重叠
          onClose: () => { // 统一关闭行为
            if (!notificationManager.isBulkClosing) {
              // 只有当实例还在管理器中时才处理（避免重复处理）
              if (notificationManager.instances.includes(instance)) {
                // 移除重复的 instance.close() 调用，避免无限递归
                // 这里只需要清理管理器中的实例，不需要再次调用 close()
                notificationManager.instances = notificationManager.instances.filter(item => item !== instance)
                notificationManager.remainingCount--
                // 更新所有弹窗的"关闭所有"按钮文本
                notificationManager.instances.forEach(inst => {
                  const closeAllBtn = inst.$el.querySelector('#close-all-btn')
                  if (closeAllBtn) {
                    closeAllBtn.textContent = `关闭所有(${notificationManager.remainingCount})`
                  }
                })
              }
            }
          }
        });

        // 将实例添加到管理器
        notificationManager.add(instance);
      }
    },
    // 标记公告为关闭状态
    async markAsRead(msgIds) {
      const params = { msg_nos: msgIds }
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesBulkMsgCancelPop(params))
      if (err) {
        this.$message.error(err.message)
        throw err // 抛出错误让调用方处理
      }
      if (res.code !== 0) {
        this.$message.error(res.msg)
        throw new Error(res.msg)
      }
      return res
    },
    // 预警已读
    async readWarning(data) {
      const params = { data: data.data, red_state: false }
      const [err, res] = await to(this.$apis.apiBackgroundMessagesWarnBoxBoxReadPost(params))
      if (err) {
        this.$message.error(err.message)
        throw err // 抛出错误让调用方处理
      }
      if (res.code !== 0) {
        this.$message.error(res.msg)
        throw new Error(res.msg)
      }
      return res
    },
    // 获取公告消息
    async getPopMsg() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundMessagesMessagesGetPopMsg())
      if (err || res.code !== 0) {
        this.$message.error(err.message)
        return false
      }
      this.allNotificaData = res.data || []
      if (res.data.important_popup_messages.length) {
        this.announcementVisible = true // 重要公告弹窗
        await this.$nextTick(); // 等待DOM更新
      } else {
        // 获取问卷+侧弹信息
        await this.getSurveyMessage(this.org)
      }
      // 服务有效期弹窗层级最高
      if (this.stopServiceMsg?.code === 5) {
        const hasShown = getSessionStorage('serviceExpireShown') || ''
        if (hasShown !== 'true') {
          // 待优化定时器
          setTimeout(() => {
            this.serviceDialogShow = true
            setSessionStorage('serviceExpireShown', 'true')
          }, 1000)
        }
      }
      // 首次获取公告后设置false 后面其他场景不需要再次调取
      this.SET_ISFIRSTGETPOPMSG(false)
    },
    // 刷新页面
    reload() {
      this.isRouterAlive = false
      this.$nextTick(_ => {
        this.isRouterAlive = true
      })
    },
    async isOpenDialog() {
      this.orgsList = this.userInfo.organizationList
      // for (let key in this.userInfo.orgs) {
      //   this.orgsList.push({
      //     id: key,
      //     name: this.userInfo.orgs[key]
      //   })
      // }
      if (this.orgsList.length > 1 && !sessionStorage.getItem('isChoiceOrg')) {
        this.dialogSync = true
        this.dialogTitle = '选择登录组织'
        this.dialogType = 'identityChange'
      } else if (this.userInfo.has_first_login) {
        this.dialogSync = true
        this.dialogTitle = '温馨提示'
        this.dialogType = 'wechatBind'
        if (this.$store.getters.userInfo.role_name !== '超级管理员' && this.$store.getters.isFirstGetPopMsg) {
          await this.getPopMsg() // 获取公告
        }
      } else {
        var id = this.orgsList[0] ? this.orgsList[0].id : ''
        this.getAbcAgreement(id)
        if (this.$store.getters.userInfo.role_name !== '超级管理员' && this.$store.getters.isFirstGetPopMsg) {
          await this.getPopMsg() // 获取公告
        }
      }
    },
    // 确认选择组织
    async confirmOrg() {
      if (this.org === '') {
        return this.$message.error('请选择组织')
      }
      if (this.dialogLoading) return
      this.dialogLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundBindOrgPost({
        org_no: this.org
      }))
      this.dialogLoading = true
      if (err) {
        this.$message.error(err.message)
      }
      if (res.code === 0) {
        sessionStorage.setItem('isChoiceOrg', 1)
        this.$store.dispatch('user/setOrganization', Number(this.org))
        let obj = {
          ...res.data,
          orgs_level: this.userInfo.orgs_level
        }
        this.$store.dispatch('user/setUserInfo', obj)
        // 更新路由
        const permissions = await this.$store.dispatch('user/getPermissionList', { key: '' })
        await this.$store.dispatch('permission/changeRoutes', permissions)

        // 刷新当前页面的数据
        this.reload()

        if (this.userInfo.has_first_login) {
          this.dialogSync = true
          this.dialogTitle = '温馨提示'
          this.dialogType = 'wechatBind'
        } else {
          this.dialogSync = false
        }
        // 获取公告
        // this.announcementVisible = true
        if (this.$store.getters.userInfo.role_name !== '超级管理员' && this.$store.getters.isFirstGetPopMsg) {
          await this.getPopMsg()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取问卷消息
    async getSurveyMessage(id, isNext = true) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundMessagesMessagesGetQuestionnaireMsgNumPost())
      if (err) return
      if (res.code === 0) {
        console.log('getSurveyMessage', res);
        let result = res.data.last_questionnaire_popup_message || null
        if (result) {
          // this.surveyDialogVisible = true
          this.surveyName = result.title
          this.surveyId = result.id
          // 如果有问卷调查 也插入在侧边弹窗的最后
          const item = {
            id: result.id,
            // type: '问卷调查',
            title: result.title
            // title: `您有一份${obj.title}问卷调查，如无法及时填写，可通过右上角通知（小喇叭）找到我~`
          }
          this.allNotificaData.popup_messages.push(item)
        }
        await this.getWarningMessage(id, isNext)
      }
    },
    // 获取预警信息
    async getWarningMessage(id, isNext) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundMessagesWarnBoxBoxListPost())
      if (err) return
      if (res.code === 0) {
        console.log('看看res.data', res.data)
        let result = res.data || null
        if (result.length) {
          result.forEach(item => {
            const obj = {
              id: item.key,
              // type: '问卷调查',
              title: `有${item.count}条新的${item.name}`,
              type: 'warning',
              dateType: item.date_type,
              data: item
              // title: `您有一份${obj.title}问卷调查，如无法及时填写，可通过右上角通知（小喇叭）找到我~`
            }
            this.allNotificaData.popup_messages.unshift(obj)
          })
        }
        await this.showNotifications()
        if (isNext) {
          this.getAbcAgreement(id)
        }
      }
    },
    gotoSetting() {
      this.$router.push({
        name: 'AccountSetting'
      })
      this.dialogSync = false
    },
    //  同意阅读协议
    clickReadAgreement() {
      this.isOpenDialog()
    },
    // 获取协议
    async getAbcAgreement(id) {
      console.log("getAbcAgreement", id);
      var params = {
        organization_id: id
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationAccountGetUserAbcAgreementPost(params))
      console.log("getAbcAgreement", err, res);
      if (err) {
        return
      }
      if (res && res.code === 0) {
        console.log("getAbcAgreement", res);
        var data = res.data || {}
        if (data && Reflect.has(data, "is_show")) {
          var isShow = data.is_show || false
          // 显示弹窗
          setSessionStorage("showAgreement", isShow)
          this.$store.dispatch('user/setAgreementInfo', data)
        }
      }
    },
    // 关闭重要公告弹窗回调
    handleCloseAnnouncementDialog() {
      this.getSurveyMessage(null, false)
    },

    // 关闭服务到期弹窗
    async handleClose(type) {
      if (type === 'surve') {
        this.serviceDialogShow = false
      } else {
        const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesBulkQuestionnaireMsgCancelPopPost({
          msg_nos: [this.surveyId]
        }))
        if (err) return
        if (res.code === 0) {
          this.surveyDialogVisible = false
        }
      }
    },
    goUpgrade() {
      this.handleClose()
      this.$router.push({
        path: '/upgrade/service'
      })
    },
    async gotoPath() {
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesBulkQuestionnaireMsgCancelPopPost({
        msg_nos: [this.surveyId]
      }))
      if (err) return
      if (res.code === 0) {
        this.surveyDialogVisible = false
        this.$router.push({
          name: 'MerchantNoticeList'
        })
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/index.scss';
@import '~@/styles/mixin.scss';
// 侧边栏消息通知全部重叠在一起
.custom-notify {
  top: 80px !important;
  right: 20px !important;
  z-index: 2000 !important; // 确保所有通知有相同的z-index
  margin: 0; // 移除默认边距
  .el-notification__content {
    margin: 10px 0 0 0;
  }
}
// #close-all-btn {
//   user-select: none; /* 防止文字被选中 */
// }
.ps-container {
  height: 100%;
  // border: 1px solid #eee;
  .el-header {
    padding: 0;
    background-color: $headerBg;
    color: #333;
  }
  .el-aside {
    color: #333;
  }
  .ps-aside {
    background-color: rgb(238, 241, 246);
  }
}
.tips {
  .goto-btn {
    width: 50%;
    display: block;
    margin: auto;
    color: #ffffff;
    background-blend-mode: normal, normal;
    box-shadow: 0px 5px 7px 0px rgba(255, 155, 69, 0.5);
    border-radius: 4px;
    border: none;
    @include btn_color_linear($btn-origin-linear);
    &:hover {
      opacity: 0.8;
      color: #ffffff !important;
    }
    &:active {
      opacity: 0.9;
      color: #ffffff !important;
    }
  }
}
</style>
