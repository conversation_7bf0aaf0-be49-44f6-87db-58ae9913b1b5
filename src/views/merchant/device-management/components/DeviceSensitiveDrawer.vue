<template>
  <div class="drawer-box">
    <customDrawer :show.sync="showDrawer" :loading="isLoading" :title="title" :size="850" @confirm="saveSetting">
      <div class="drawer-container">
        <div class="red">配置离线时，设备触发脱机提示弹窗阈值</div>
        <div class="drawer-content">
          <div class="m-b-10">
            情况1:当设备在
            <el-input-number v-model="drawerFormData.desensitize_minute_one" :min="3" size="mini"></el-input-number>
            分钟内，网络健康检查接口连续产生次
            <el-input-number
              v-model="drawerFormData.desensitize_minute_one_time"
              :min="3"
              size="mini"
            ></el-input-number>
            超时；
          </div>
          <div>
            情况2:当下单接口连续
            <el-input-number v-model="drawerFormData.desensitize_minute_two" :min="2" size="mini"></el-input-number>
            次超时
            <el-input-number
              v-model="drawerFormData.desensitize_minute_two_time"
              :min="5"
              size="mini"
            ></el-input-number>
            秒以上。
          </div>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
export default {
  name: 'DeviceSensitiveDrawer',
  components: {},
  props: {
    isshow: Boolean,
    dialogInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      isLoading: false,
      title: '脱敏设置',
      drawerFormData: {
        desensitize_minute_one: '3',
        desensitize_minute_one_time: '3',
        desensitize_minute_two: '2',
        desensitize_minute_two_time: '5'
      }
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.getDeviceSettings()
  },
  methods: {
    closeClick() {
      this.showDrawer = false
    },
    async getDeviceSettings() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        organization_id: this.$store.getters.organization
      }
      const res = await this.$apis.apiBackgroundDeviceDeviceGetDeviceSettingsPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.drawerFormData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    saveSetting() {
      this.setSensitiveSetting()
    },
    async setSensitiveSetting() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        organization_id: this.$store.getters.organization,
        desensitize_minute_one: this.drawerFormData.desensitize_minute_one,
        desensitize_minute_one_time: this.drawerFormData.desensitize_minute_one_time,
        desensitize_minute_two: this.drawerFormData.desensitize_minute_two,
        desensitize_minute_two_time: this.drawerFormData.desensitize_minute_two_time
      }
      const res = await this.$apis.apiBackgroundAdminDeviceSetDeviceSettingsPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.showDrawer = false
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  padding: 20px;
  .drawer-content {
    margin-top: 20px;
    padding: 20px;
    background: #f2f2f2;
    border-radius: 20px;
  }
}
</style>
