<template>
  <div class="guidance_rules">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content ps-flex" v-loading="isLoading">
        <div class="model m-r-30" v-for="(item, index) in tableData" :key="index">
          <div class="model-content">
            <div>
              <div class="title">{{ item.name }}</div>
              <div class="content">
                {{item.remark}}
              </div>
            </div>
            <div class="update-time">更新时间：{{ item.update_time }}</div>
          </div>
          <div class="bottom-state">
            <div>
              <span class="p-r-10" style="font-size:13px">状态</span>
              <el-switch
                v-model="item.status"
                active-color="#ff9b45"
                active-value="enable"
                inactive-value="disable"
                @change="getNutritionRuleModify(item)"
              ></el-switch>
            </div>
            <el-button class="ps-origin-plain-btn" size="mini" @click="modifyClick('modify', item)">
              编辑
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="totalCount"
        background
        class="ps-text"
        popper-class="ps-popper-select"
      ></el-pagination>
    </div>
    <!-- 分页 end -->
  </div>
</template>

<script>
import { to } from '@/utils'
export default {
  data() {
    return {
      isLoading: false,
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getNutritionRuleList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    async getNutritionRuleList() {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminNutritionRuleListPost({
          type: 'nutrition_guide',
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 状态
    async getNutritionRuleModify(row) {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminNutritionRuleModifyPost({
          id: row.id,
          status: row.status
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getNutritionRuleList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getNutritionRuleList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    modifyClick(type, row) {
      this.$router.push({
        name: 'SuperModifyGuidanceRules',
        query: {
          type: type,
          data: type === 'modify' ? this.$encodeQuery(row) : ''
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.guidance_rules {
  .model {
    width: 300px;
    height: 300px;
    border: 1px solid #ccc;
    border-radius: 10px;
    .model-content {
      height: 240px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 15px;
      .title {
        padding-bottom: 10px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
      .content {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 7;
        -webkit-box-orient: vertical;
        font-size: 13px;
      }
    }
    .update-time {
      font-size: 13px;
    }
    .bottom-state {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border-top: 1px solid #ccc;
    }
  }
}
</style>
